package com.flutterup.app.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBackground
import com.flutterup.app.design.text.DisplayTitle
import com.flutterup.app.design.theme.AppTheme

@Composable
fun LoginScreen(
    modifier: Modifier = Modifier,
    searchViewModel: LoginViewModel = hiltViewModel(),
) {
    Surface(
        modifier = modifier.fillMaxSize()
    ) {
        Image(
            painter = painterResource(id = R.mipmap.ic_login_background),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )

        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            DisplayTitle("HELLO")
        }

        
    }
}


@Preview
@Composable
private fun LoginScreenPreview() {
    AppTheme {
        LoginScreen()
    }
}