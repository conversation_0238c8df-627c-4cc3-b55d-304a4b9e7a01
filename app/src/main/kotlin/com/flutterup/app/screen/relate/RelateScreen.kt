package com.flutterup.app.screen.relate

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.flutterup.app.screen.LocalInnerPadding


@Composable
fun RelateScreen(
    modifier: Modifier = Modifier,
) {
    val innerPadding = LocalInnerPadding.current

    Box(
        modifier = modifier.fillMaxSize().padding(bottom = innerPadding.calculateBottomPadding())
    ) {
    }
}