package com.flutterup.app.screen.discover

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.flutterup.app.screen.LocalInnerPadding
import com.flutterup.app.screen.LocalNavController

@Composable
fun DiscoverScreen(
    modifier: Modifier = Modifier,
) {
    val innerPadding = LocalInnerPadding.current
    val navController = LocalNavController.current

    Box(
        modifier = modifier.fillMaxSize().padding(bottom = innerPadding.calculateBottomPadding())
    ) {
    }
}