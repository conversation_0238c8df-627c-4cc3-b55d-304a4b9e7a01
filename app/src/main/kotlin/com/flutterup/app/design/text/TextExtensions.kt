package com.flutterup.app.design.text

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp

/**
 * 文字样式扩展
 * 提供一些常用的文字样式变体
 */
object TextExtensions {

    /**
     * 获取强调样式
     */
    @Composable
    fun TextStyle.emphasis(): TextStyle = this.copy(
        fontWeight = FontWeight.SemiBold,
        color = MaterialTheme.colorScheme.primary
    )

    /**
     * 获取次要样式
     */
    @Composable
    fun TextStyle.secondary(): TextStyle = this.copy(
        color = MaterialTheme.colorScheme.onSurfaceVariant
    )

    /**
     * 获取禁用样式
     */
    @Composable
    fun TextStyle.disabled(): TextStyle = this.copy(
        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
    )

    /**
     * 获取错误样式
     */
    @Composable
    fun TextStyle.error(): TextStyle = this.copy(
        color = MaterialTheme.colorScheme.error
    )

    /**
     * 获取成功样式
     */
    @Composable
    fun TextStyle.success(): TextStyle = this.copy(
        color = Color(0xFF4CAF50) // 绿色
    )

    /**
     * 获取警告样式
     */
    @Composable
    fun TextStyle.warning(): TextStyle = this.copy(
        color = Color(0xFFFF9800) // 橙色
    )

    /**
     * 获取信息样式
     */
    @Composable
    fun TextStyle.info(): TextStyle = this.copy(
        color = Color(0xFF2196F3) // 蓝色
    )
}

/**
 * 常用的文字颜色
 */
object TextColors {
    
    @Composable
    fun primary() = MaterialTheme.colorScheme.primary
    
    @Composable
    fun onPrimary() = MaterialTheme.colorScheme.onPrimary
    
    @Composable
    fun secondary() = MaterialTheme.colorScheme.secondary
    
    @Composable
    fun onSecondary() = MaterialTheme.colorScheme.onSecondary
    
    @Composable
    fun surface() = MaterialTheme.colorScheme.surface
    
    @Composable
    fun onSurface() = MaterialTheme.colorScheme.onSurface
    
    @Composable
    fun onSurfaceVariant() = MaterialTheme.colorScheme.onSurfaceVariant
    
    @Composable
    fun error() = MaterialTheme.colorScheme.error
    
    @Composable
    fun onError() = MaterialTheme.colorScheme.onError
    
    @Composable
    fun outline() = MaterialTheme.colorScheme.outline
    
    @Composable
    fun success() = Color(0xFF4CAF50)
    
    @Composable
    fun warning() = Color(0xFFFF9800)
    
    @Composable
    fun info() = Color(0xFF2196F3)
    
    @Composable
    fun disabled() = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
    
    @Composable
    fun placeholder() = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
}

/**
 * 常用的字体权重
 */
object TextWeights {
    val Thin = FontWeight.Thin
    val ExtraLight = FontWeight.ExtraLight
    val Light = FontWeight.Light
    val Normal = FontWeight.Normal
    val Medium = FontWeight.Medium
    val SemiBold = FontWeight.SemiBold
    val Bold = FontWeight.Bold
    val ExtraBold = FontWeight.ExtraBold
    val Black = FontWeight.Black
}

/**
 * 常用的字体大小
 */
object TextSizes {
    val ExtraSmall = 10.sp
    val Small = 12.sp
    val Medium = 14.sp
    val Large = 16.sp
    val ExtraLarge = 18.sp
    val Huge = 20.sp
    val Title = 22.sp
    val Headline = 24.sp
    val Display = 28.sp
}

/**
 * 文字样式预设
 */
object TextPresets {
    
    /**
     * 页面主标题
     */
    @Composable
    fun pageTitle() = MaterialTheme.typography.headlineLarge.copy(
        fontWeight = FontWeight.Bold,
        color = MaterialTheme.colorScheme.onSurface
    )
    
    /**
     * 页面副标题
     */
    @Composable
    fun pageSubtitle() = MaterialTheme.typography.titleMedium.copy(
        color = MaterialTheme.colorScheme.onSurfaceVariant
    )
    
    /**
     * 卡片标题
     */
    @Composable
    fun cardTitle() = MaterialTheme.typography.titleMedium.copy(
        fontWeight = FontWeight.SemiBold,
        color = MaterialTheme.colorScheme.onSurface
    )
    
    /**
     * 卡片内容
     */
    @Composable
    fun cardContent() = MaterialTheme.typography.bodyMedium.copy(
        color = MaterialTheme.colorScheme.onSurfaceVariant
    )
    
    /**
     * 按钮文字
     */
    @Composable
    fun button() = MaterialTheme.typography.labelLarge.copy(
        fontWeight = FontWeight.Medium
    )
    
    /**
     * 输入框标签
     */
    @Composable
    fun inputLabel() = MaterialTheme.typography.bodySmall.copy(
        fontWeight = FontWeight.Medium,
        color = MaterialTheme.colorScheme.onSurfaceVariant
    )
    
    /**
     * 输入框提示
     */
    @Composable
    fun inputHint() = MaterialTheme.typography.bodyMedium.copy(
        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
    )
    
    /**
     * 错误提示
     */
    @Composable
    fun errorMessage() = MaterialTheme.typography.bodySmall.copy(
        color = MaterialTheme.colorScheme.error
    )
    
    /**
     * 链接文字
     */
    @Composable
    fun link() = MaterialTheme.typography.bodyMedium.copy(
        color = MaterialTheme.colorScheme.primary,
        fontWeight = FontWeight.Medium
    )
    
    /**
     * 标签文字
     */
    @Composable
    fun tag() = MaterialTheme.typography.labelSmall.copy(
        fontWeight = FontWeight.Medium
    )
    
    /**
     * 导航文字
     */
    @Composable
    fun navigation() = MaterialTheme.typography.labelMedium.copy(
        fontWeight = FontWeight.Medium
    )
}
